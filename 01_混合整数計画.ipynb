import pandas as pd
import pulp
import csv
import random
import matplotlib.pyplot as plt
import japanize_matplotlib
import copy

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []

# コストとペナルティの係数
在庫コスト単価 = 180
残業コスト単価 = 66.7
段替えコスト単価 = 130
出荷遅れコスト単価 = 500

# 稼働時間（分）
定時 = 8 * 60 * 2
最大残業時間 = 2 * 60 * 2
段替え時間 = 30
期間 = 20

# CSVファイルを読み込む関数
def read_csv(file_path):
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        
        rows = list(reader)
        for row in rows:
            if len(row) == 0:
                continue
            品番リスト.append(row[header.index("part_number")])
            出荷数リスト.append(int(row[header.index("shipment")]))
            収容数リスト.append(int(row[header.index("capacity")]))
            
            cycle_time_per_unit = float(row[header.index("cycle_time")]) / 60
            サイクルタイムリスト.append(cycle_time_per_unit)
            
            込め数リスト.append(int(row[header.index("cabity")]))
    
    # read_csvからは初期在庫量を返さないように変更
    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト

# 修正：初期在庫量を引数として受け取るように変更
def solve_mip(initial_inventory_list_arg):
    """PuLPを用いてMIPを解く関数"""
    
    # 修正：ここではread_csvを呼び出さない
    品番数 = len(品番リスト)
    
    # モデルの定義
    model = pulp.LpProblem("ProductionScheduling", pulp.LpMinimize)
    
    # インデックスの定義
    品目 = range(品番数)
    期間_index = range(期間)

    # 決定変数
    Production = pulp.LpVariable.dicts("Production", (品目, 期間_index), lowBound=0, cat='Integer')
    IsProduced = pulp.LpVariable.dicts("IsProduced", (品目, 期間_index), cat='Binary')
    Inventory = pulp.LpVariable.dicts("Inventory", (品目, 期間_index), lowBound=0, cat='Continuous')
    Shortage = pulp.LpVariable.dicts("Shortage", (品目, 期間_index), lowBound=0, cat='Continuous')
    WorkTime = pulp.LpVariable.dicts("WorkTime", 期間_index, lowBound=0, cat='Continuous')
    Overtime = pulp.LpVariable.dicts("Overtime", 期間_index, lowBound=0, cat='Continuous')

    # 目的関数
    total_cost = pulp.lpSum(
        在庫コスト単価 * Inventory[i][t] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        残業コスト単価 * Overtime[t] for t in 期間_index
    ) + pulp.lpSum(
        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index
    )
    
    model += total_cost, "Total Cost"

    # 制約条件
    bigM = 1000000

    for i in 品目:
        for t in 期間_index:
            if t == 0:
                # 修正：引数で受け取った初期在庫リストを使用
                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]
            else:
                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]
            
            model += Production[i][t] <= bigM * IsProduced[i][t]

    for t in 期間_index:
        model += WorkTime[t] == pulp.lpSum(
            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]
            for i in 品目
        )
        
        model += WorkTime[t] <= 定時 + Overtime[t]
        model += WorkTime[t] <= 定時 + 最大残業時間
        model += Overtime[t] >= WorkTime[t] - 定時
        model += Overtime[t] >= 0

    # Solverの設定
    solver = pulp.GUROBI(msg=True)
    
    # 最適化の実行
    model.solve(solver)
    
    print("ステータス:", pulp.LpStatus[model.status])
    if pulp.LpStatus[model.status] == 'Optimal':
        print("総コスト:", pulp.value(model.objective))

        production_schedule = [[0] * 期間 for _ in range(品番数)]
        for i in 品目:
            for t in 期間_index:
                production_schedule[i][t] = pulp.value(Production[i][t])

        return production_schedule, pulp.value(model.objective)
    
    return None, None

def plot_results(best_individual, initial_inventory):
    """結果をプロットする関数"""
    global 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 期間

    print("\n=== 結果のプロット ===")
    
    total_inventory_per_period = []
    total_production_time_per_period = []
    total_setup_times_per_period = []
    total_shipment_delay_per_period = []

    inventory = initial_inventory[:]
    max_daily_work_time = (8 + 2) * 60 * 2
    daily_regular_time = 8 * 60 * 2
    
    for t in range(期間):
        daily_inventory = 0
        daily_production_time = 0
        daily_setup_times = 0
        daily_shipment_delay = 0
        
        # 今回の期間の在庫と生産量、遅れを計算
        temp_inventory = inventory[:]
        for i in range(len(品番リスト)):
            production = best_individual[i][t]
            if production > 0:
                daily_setup_times += 1
                setup_time = 30
            else:
                setup_time = 0

            production_time = production * (サイクルタイムリスト[i] / 込め数リスト[i])
            daily_production_time += production_time + setup_time
            temp_inventory[i] += production - 出荷数リスト[i]

        for i in range(len(品番リスト)):
            inventory[i] = temp_inventory[i]
            if inventory[i] < 0:
                daily_shipment_delay += abs(inventory[i])
            daily_inventory += inventory[i]
            
        total_inventory_per_period.append(daily_inventory)
        total_production_time_per_period.append(daily_production_time)
        total_setup_times_per_period.append(daily_setup_times)
        total_shipment_delay_per_period.append(daily_shipment_delay)

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    periods = list(range(1, 期間 + 1))

    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')
    axes[0, 0].set_xlabel('期間')
    axes[0, 0].set_ylabel('総在庫量 (個)')
    axes[0, 0].grid(True, alpha=0.3)

    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')
    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')
    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')
    axes[0, 1].set_xlabel('期間')
    axes[0, 1].set_ylabel('総稼働時間 (分)')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].legend()

    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')
    axes[1, 0].set_xlabel('期間')
    axes[1, 0].set_ylabel('総段替え回数（回）')
    axes[1, 0].grid(True, alpha=0.3)

    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)
    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')
    axes[1, 1].set_xlabel('期間')
    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)
    print(f"時間制約違反: {time_violations} 期間")

def adjust_initial_inventory(initial_inventory_list, h_cost, c_cost, num_simulations=10, 期間=20):
    """初期在庫を更新する関数"""
    
    品番数 = len(initial_inventory_list)
    adjusted_initial_inventory = copy.deepcopy(initial_inventory_list)
    
    # h / (h+c)
    target_service_level = h_cost / (h_cost + c_cost)
    
    print(f"\n=== 在庫調整アルゴリズム実行 ===")
    
    for iteration in range(5):
        print(f"--- Iteration {iteration + 1} ---")
        
        inventory_history = [[] for _ in range(品番数)]
        
        # 在庫分布を得る
        for sim in range(num_simulations):            
            inventory = adjusted_initial_inventory[:]
            
            for t in range(期間):
                for i in range(品番数):
                    demand = 出荷数リスト[i]  # 出荷数を使用
                    inventory[i] -= demand
                
                for i in range(品番数):
                    if inventory[i] < 0:
                        production = abs(inventory[i])  # 出荷に不足する分だけ生産する
                        inventory[i] += production
                
                for i in range(品番数):
                    inventory_history[i].append(inventory[i])
        
        adjustment_made = False
        
        for i in range(品番数):
            product_inventory_data = pd.Series(inventory_history[i])
            inventory_counts = product_inventory_data.value_counts(normalize=True).sort_index()
            
            # 最適なrを計算
            cumulative_distribution = 0
            optimal_r = 0
            for stock_level, probability in inventory_counts.items():
                cumulative_distribution += probability
                if cumulative_distribution > target_service_level:
                    optimal_r = stock_level
                    break
            
            current_s = adjusted_initial_inventory[i]
            adjustment_amount = current_s - optimal_r
            
            if abs(adjustment_amount) > 0.5:  # Use a small tolerance
                adjusted_initial_inventory[i] = max(0, current_s - adjustment_amount)
                adjustment_made = True
                print(f"  製品 {品番リスト[i]}: 在庫調整量* = {adjustment_amount:.2f}, 新初期在庫量 = {adjusted_initial_inventory[i]}")

        if not adjustment_made:
            print("--- 収束しました。アルゴリズムを終了します。 ---")
            break
        
    return adjusted_initial_inventory


if __name__ == "__main__":
    
    # 修正：read_csvから受け取る変数から初期在庫量リストを削除
    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト = read_csv('data.csv')
    
    # 添付文書のアルゴリズムを適用するためのパラメータ
    # These are illustrative values, you'd need to set them based on your problem
    h_cost = 180  # Example unit holding cost
    c_cost = 5000 # Example unit shortage cost
    
    # 新しい初期在庫リストを生成 (ここでは一旦ランダムな値を使用)
    initial_inventory_list = [random.randint(shipment * 3, shipment * 5) for shipment in 出荷数リスト]

    print("--- 元の初期在庫 ---")
    for i, stock in enumerate(initial_inventory_list):
        print(f"製品 {品番リスト[i]}: {stock}")
        
    # 添付文書のアルゴリズムを適用して初期在庫を調整
    adjusted_initial_inventory = adjust_initial_inventory(initial_inventory_list, h_cost, c_cost)

    print("\n--- 調整後の初期在庫 ---")
    for i, stock in enumerate(adjusted_initial_inventory):
        print(f"製品 {品番リスト[i]}: {stock}")

    # 調整された初期在庫リストを使ってMIPソルバーを実行
    # 修正：グローバル変数を更新
    初期在庫量リスト = adjusted_initial_inventory
    # 修正：solve_mipに引数として初期在庫を渡す
    best_solution, best_cost = solve_mip(初期在庫量リスト)
    
    if best_solution:
        print(f"\n=== 最適化結果 ===")
        print(f"最良個体の総コスト: {best_cost:.2f}")

        # 結果をDataFrameで表示
        品番数 = len(品番リスト)
        df_schedule = pd.DataFrame(best_solution, index=品番リスト, columns=[f'Day_{t+1}' for t in range(期間)])
        print("\n--- 生産スケジュール ---")
        print(df_schedule)
        
        # プロット
        plot_results(best_solution, 初期在庫量リスト)
    else:
        print("\n解が見つかりませんでした")